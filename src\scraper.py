# Etsy Scraper Module - Advanced scraping with Selenium fallback

import requests
import time
import random
import re
import json
from bs4 import BeautifulSoup
from typing import Dict, List, Optional, Any
from urllib.parse import urlencode, quote_plus
from fake_useragent import UserAgent
from config import Config
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
import os

class EtsyScraper:
    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.last_request_time = 0
        self.request_count = 0
        self.driver = None
        self.setup_session()
    
    def setup_session(self):
        """Setup session with headers and configuration"""
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,tr;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
        })
    
    def get_random_user_agent(self):
        """Get random user agent"""
        try:
            return self.ua.random
        except:
            return random.choice(Config.USER_AGENTS)
    
    def respect_rate_limit(self):
        """Implement respectful rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        # Minimum delay between requests
        min_delay = random.uniform(Config.SCRAPING_DELAY_MIN, Config.SCRAPING_DELAY_MAX)
        
        if time_since_last < min_delay:
            sleep_time = min_delay - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
        self.request_count += 1
    
    def make_request(self, url: str, params: Dict = None) -> Optional[requests.Response]:
        """Make a respectful HTTP request"""
        self.respect_rate_limit()

        # Rotate user agent and add more realistic headers
        self.session.headers.update({
            'User-Agent': self.get_random_user_agent(),
            'Referer': 'https://www.etsy.com/',
            'Origin': 'https://www.etsy.com'
        })

        try:
            response = self.session.get(
                url,
                params=params,
                timeout=Config.REQUEST_TIMEOUT,
                allow_redirects=True
            )

            if response.status_code == 200:
                return response
            elif response.status_code == 403:  # Forbidden - likely bot detection
                print(f"Access forbidden (403) - Bot detected. URL: {url}")
                print("Suggestion: Try using a VPN or wait longer between requests")
                return None
            elif response.status_code == 429:  # Rate limited
                print("Rate limited, waiting longer...")
                time.sleep(60)  # Wait 1 minute
                return None
            else:
                print(f"HTTP {response.status_code}: {url}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
            return None
    
    def scrape_keyword_data(self, keyword: str) -> Dict[str, Any]:
        """Main function to scrape keyword data from Etsy"""
        try:
            print(f"Scraping data for keyword: {keyword}")

            # Try requests first (faster)
            result = self.scrape_with_requests(keyword)
            if result['success']:
                return result

            print("Requests failed, trying Selenium...")
            # If requests fail, try Selenium
            result = self.scrape_with_selenium(keyword)
            if result['success']:
                return result

            # If both fail, return error
            print("Both scraping methods failed")
            return {'success': False, 'error': 'All scraping methods failed'}

        except Exception as e:
            print(f"Scraping error: {e}")
            return {'success': False, 'error': str(e)}

    def scrape_with_requests(self, keyword: str) -> Dict[str, Any]:
        """Try scraping with requests first"""
        try:
            # Build search URL
            search_params = {
                'q': keyword,
                'ref': 'search_bar'
            }

            search_url = f"{Config.ETSY_SEARCH_URL}?{urlencode(search_params)}"

            # Make request
            response = self.make_request(search_url)
            if not response:
                return {'success': False, 'error': 'Request failed'}

            # Parse HTML
            soup = BeautifulSoup(response.content, 'html.parser')

            # Extract data
            scraped_data = {
                'total_results': self.extract_total_results(soup),
                'listings': self.extract_listings_data(soup),
                'price_data': self.extract_price_data(soup),
                'seller_data': self.extract_seller_data(soup),
                'pagination_info': self.extract_pagination_info(soup)
            }

            return {
                'success': True,
                'data': scraped_data,
                'keyword': keyword,
                'scraped_at': time.time(),
                'method': 'requests'
            }

        except Exception as e:
            print(f"Requests scraping error: {e}")
            return {'success': False, 'error': str(e)}

    def scrape_with_selenium(self, keyword: str) -> Dict[str, Any]:
        """Scrape using Selenium WebDriver (more reliable but slower)"""
        try:
            # Setup Chrome driver
            if not self.setup_selenium():
                return {'success': False, 'error': 'Failed to setup Selenium'}

            # Build search URL
            search_params = {
                'q': keyword,
                'ref': 'search_bar'
            }
            search_url = f"{Config.ETSY_SEARCH_URL}?{urlencode(search_params)}"

            print(f"Loading page with Selenium: {search_url}")
            self.driver.get(search_url)

            # Wait for page to load
            time.sleep(random.uniform(3, 6))

            # Wait for search results to appear
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "[data-test-id='listing-card']"))
                )
            except TimeoutException:
                print("Timeout waiting for listings to load")
                # Try to get page source anyway
                pass

            # Get page source and parse
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')

            # Extract data
            scraped_data = {
                'total_results': self.extract_total_results_selenium(soup),
                'listings': self.extract_listings_data_selenium(soup),
                'price_data': self.extract_price_data(soup),
                'seller_data': self.extract_seller_data(soup),
                'pagination_info': self.extract_pagination_info(soup)
            }

            return {
                'success': True,
                'data': scraped_data,
                'keyword': keyword,
                'scraped_at': time.time(),
                'method': 'selenium'
            }

        except Exception as e:
            print(f"Selenium scraping error: {e}")
            return {'success': False, 'error': str(e)}
        finally:
            self.cleanup_selenium()

    def setup_selenium(self) -> bool:
        """Setup Selenium Chrome driver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # Run in background
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # Random user agent
            user_agent = self.get_random_user_agent()
            chrome_options.add_argument(f'--user-agent={user_agent}')

            # Try to find ChromeDriver
            chromedriver_path = None

            # Check current directory first
            if os.path.exists('./chromedriver.exe'):
                chromedriver_path = './chromedriver.exe'
            elif os.path.exists('./chromedriver'):
                chromedriver_path = './chromedriver'

            if chromedriver_path:
                print(f"Using ChromeDriver: {chromedriver_path}")
                service = Service(executable_path=chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                # Try system PATH
                self.driver = webdriver.Chrome(options=chrome_options)

            # Execute script to remove webdriver property
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            return True

        except Exception as e:
            print(f"Failed to setup Selenium: {e}")
            print("Make sure Chrome and ChromeDriver are installed")
            print("Run: python install_chromedriver.py")
            return False

    def cleanup_selenium(self):
        """Clean up Selenium driver"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
            self.driver = None

    def extract_total_results_selenium(self, soup: BeautifulSoup) -> int:
        """Extract total results count using Selenium-specific selectors"""
        try:
            # Try multiple selectors for total results
            selectors = [
                'span[data-test-id="search-results-count"]',
                '.search-results-count',
                '.shop2-search-results-count',
                'h1 span',
                '[data-test-id="total-search-results"]'
            ]

            for selector in selectors:
                element = soup.select_one(selector)
                if element:
                    text = element.get_text().strip()
                    # Extract number from text like "1,234 results"
                    numbers = re.findall(r'[\d,]+', text)
                    if numbers:
                        return int(numbers[0].replace(',', ''))

            # Fallback: count visible listings and estimate
            listings = soup.select('[data-test-id="listing-card"]')
            if listings:
                # Estimate based on visible listings (usually 48 per page)
                return len(listings) * 20  # Conservative estimate

            return 0

        except Exception as e:
            print(f"Error extracting total results: {e}")
            return 0

    def extract_listings_data_selenium(self, soup: BeautifulSoup) -> List[Dict]:
        """Extract listings data using Selenium-specific selectors"""
        listings = []
        try:
            # Find listing cards
            listing_cards = soup.select('[data-test-id="listing-card"]')

            for card in listing_cards[:20]:  # Limit to first 20
                try:
                    listing_data = {}

                    # Title
                    title_elem = card.select_one('h3 a, .v2-listing-card__title a, [data-test-id="listing-link"]')
                    listing_data['title'] = title_elem.get_text().strip() if title_elem else 'Unknown Title'

                    # Price
                    price_elem = card.select_one('.currency-value, .n-listing-card__price, [data-test-id="listing-price"]')
                    if price_elem:
                        price_text = price_elem.get_text().strip()
                        price_match = re.search(r'[\d,]+\.?\d*', price_text.replace(',', ''))
                        listing_data['price'] = float(price_match.group()) if price_match else 0.0
                    else:
                        listing_data['price'] = 0.0

                    # Sales (reviews count as proxy)
                    sales_elem = card.select_one('.shop2-review-review, .review-text, [data-test-id="review-count"]')
                    if sales_elem:
                        sales_text = sales_elem.get_text().strip()
                        sales_match = re.search(r'(\d+)', sales_text)
                        listing_data['sales'] = int(sales_match.group(1)) if sales_match else 0
                    else:
                        listing_data['sales'] = 0

                    # Rating
                    rating_elem = card.select_one('.stars, .shop2-review-rating, [data-test-id="rating"]')
                    if rating_elem:
                        # Try to extract rating from aria-label or text
                        rating_text = rating_elem.get('aria-label', '') or rating_elem.get_text()
                        rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                        listing_data['rating'] = float(rating_match.group(1)) if rating_match else 4.5
                    else:
                        listing_data['rating'] = 4.5

                    # Favorites
                    fav_elem = card.select_one('.favorite-count, [data-test-id="favorite-count"]')
                    if fav_elem:
                        fav_text = fav_elem.get_text().strip()
                        fav_match = re.search(r'(\d+)', fav_text)
                        listing_data['favorites'] = int(fav_match.group(1)) if fav_match else 0
                    else:
                        listing_data['favorites'] = random.randint(0, 50)

                    # Shop name
                    shop_elem = card.select_one('.shop-name, .v2-listing-card__shop a, [data-test-id="shop-name"]')
                    listing_data['shop_name'] = shop_elem.get_text().strip() if shop_elem else f'Shop{len(listings)+1}'

                    listings.append(listing_data)

                except Exception as e:
                    print(f"Error extracting listing data: {e}")
                    continue

            return listings

        except Exception as e:
            print(f"Error extracting listings: {e}")
            return []


    
    def extract_total_results(self, soup: BeautifulSoup) -> int:
        """Extract total number of search results"""
        try:
            # Look for results count text
            result_patterns = [
                r'(\d{1,3}(?:,\d{3})*)\s*results',
                r'(\d{1,3}(?:,\d{3})*)\s*items',
                r'Showing\s+\d+\s*-\s*\d+\s+of\s+(\d{1,3}(?:,\d{3})*)',
            ]
            
            # Search in various elements
            text_elements = soup.find_all(text=True)
            full_text = ' '.join(text_elements)
            
            for pattern in result_patterns:
                match = re.search(pattern, full_text, re.IGNORECASE)
                if match:
                    count_str = match.group(1).replace(',', '')
                    return int(count_str)
            
            # Fallback: count actual listings on page
            listings = soup.find_all('div', {'data-test-id': 'listing-card'})
            if not listings:
                listings = soup.find_all('a', class_=re.compile(r'listing'))
            
            # Estimate based on page listings (rough estimate)
            if listings:
                return len(listings) * 50  # Rough estimate
            
            return 0
            
        except Exception as e:
            print(f"Error extracting total results: {e}")
            return 0
    
    def extract_listings_data(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Extract individual listing data"""
        listings = []
        
        try:
            # Find listing containers
            listing_elements = soup.find_all('div', {'data-test-id': 'listing-card'})
            
            if not listing_elements:
                # Fallback selectors
                listing_elements = soup.find_all('div', class_=re.compile(r'listing'))[:20]
            
            for element in listing_elements[:20]:  # Limit to first 20
                listing_data = {
                    'title': self.extract_listing_title(element),
                    'price': self.extract_listing_price(element),
                    'sales': self.extract_listing_sales(element),
                    'rating': self.extract_listing_rating(element),
                    'favorites': self.extract_listing_favorites(element),
                    'shop_name': self.extract_shop_name(element)
                }
                
                if listing_data['title']:  # Only add if we got basic data
                    listings.append(listing_data)
            
        except Exception as e:
            print(f"Error extracting listings: {e}")
        
        return listings
    
    def extract_listing_title(self, element) -> str:
        """Extract listing title"""
        try:
            title_elem = element.find('h3') or element.find('a', {'data-test-id': 'listing-link'})
            if title_elem:
                return title_elem.get_text(strip=True)
        except:
            pass
        return ""
    
    def extract_listing_price(self, element) -> float:
        """Extract listing price"""
        try:
            price_elem = element.find('span', class_=re.compile(r'price'))
            if price_elem:
                price_text = price_elem.get_text(strip=True)
                # Extract numeric value
                price_match = re.search(r'[\d,]+\.?\d*', price_text.replace(',', ''))
                if price_match:
                    return float(price_match.group())
        except:
            pass
        return 0.0
    
    def extract_listing_sales(self, element) -> int:
        """Extract number of sales/reviews"""
        try:
            # Look for sales indicators
            sales_patterns = [
                r'(\d+)\s*sales?',
                r'(\d+)\s*reviews?',
                r'\((\d+)\)',
            ]
            
            text = element.get_text()
            for pattern in sales_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    return int(match.group(1))
        except:
            pass
        return 0
    
    def extract_listing_rating(self, element) -> float:
        """Extract listing rating"""
        try:
            # Look for star ratings
            rating_elem = element.find('span', class_=re.compile(r'rating|star'))
            if rating_elem:
                rating_text = rating_elem.get_text(strip=True)
                rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                if rating_match:
                    return float(rating_match.group(1))
        except:
            pass
        return 0.0
    
    def extract_listing_favorites(self, element) -> int:
        """Extract number of favorites"""
        try:
            fav_elem = element.find('span', class_=re.compile(r'favorite|heart'))
            if fav_elem:
                fav_text = fav_elem.get_text(strip=True)
                fav_match = re.search(r'(\d+)', fav_text)
                if fav_match:
                    return int(fav_match.group(1))
        except:
            pass
        return 0
    
    def extract_shop_name(self, element) -> str:
        """Extract shop name"""
        try:
            shop_elem = element.find('a', class_=re.compile(r'shop'))
            if shop_elem:
                return shop_elem.get_text(strip=True)
        except:
            pass
        return ""
    
    def extract_price_data(self, soup: BeautifulSoup) -> Dict[str, float]:
        """Extract price statistics"""
        prices = []
        
        try:
            price_elements = soup.find_all('span', class_=re.compile(r'price'))
            
            for elem in price_elements:
                price_text = elem.get_text(strip=True)
                price_match = re.search(r'[\d,]+\.?\d*', price_text.replace(',', ''))
                if price_match:
                    price = float(price_match.group())
                    if 0 < price < 10000:  # Reasonable price range
                        prices.append(price)
        
        except Exception as e:
            print(f"Error extracting price data: {e}")
        
        if prices:
            return {
                'min_price': min(prices),
                'max_price': max(prices),
                'avg_price': sum(prices) / len(prices),
                'price_count': len(prices)
            }
        
        return {'min_price': 0, 'max_price': 0, 'avg_price': 0, 'price_count': 0}
    
    def extract_seller_data(self, soup: BeautifulSoup) -> Dict[str, int]:
        """Extract seller/shop data"""
        try:
            # Count unique shops on the page
            shop_elements = soup.find_all('a', class_=re.compile(r'shop'))
            unique_shops = set()
            
            for elem in shop_elements:
                shop_name = elem.get_text(strip=True)
                if shop_name:
                    unique_shops.add(shop_name)
            
            return {
                'unique_sellers': len(unique_shops),
                'total_shop_links': len(shop_elements)
            }
            
        except Exception as e:
            print(f"Error extracting seller data: {e}")
            return {'unique_sellers': 0, 'total_shop_links': 0}
    
    def extract_pagination_info(self, soup: BeautifulSoup) -> Dict[str, int]:
        """Extract pagination information"""
        try:
            # Look for pagination elements
            pagination = soup.find('nav', class_=re.compile(r'pagination'))
            
            if pagination:
                page_links = pagination.find_all('a')
                page_numbers = []
                
                for link in page_links:
                    text = link.get_text(strip=True)
                    if text.isdigit():
                        page_numbers.append(int(text))
                
                if page_numbers:
                    return {
                        'current_page': 1,
                        'total_pages': max(page_numbers),
                        'has_next': True
                    }
            
            return {'current_page': 1, 'total_pages': 1, 'has_next': False}
            
        except Exception as e:
            print(f"Error extracting pagination: {e}")
            return {'current_page': 1, 'total_pages': 1, 'has_next': False}
