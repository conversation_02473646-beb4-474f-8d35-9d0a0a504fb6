<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Etsy Keyword Research Tool</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <header class="row bg-primary text-white py-3 mb-4">
            <div class="col">
                <h1 class="h3 mb-0">
                    <i class="fas fa-search me-2"></i>
                    Etsy Keyword Research Tool
                </h1>
                <p class="mb-0 opacity-75">Research keywords to boost your listings' reach and attract more shoppers on Etsy</p>
            </div>
        </header>

        <!-- Search Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="text" 
                                           id="keywordInput" 
                                           class="form-control form-control-lg" 
                                           placeholder="Enter keyword to analyze (e.g., handmade jewelry)"
                                           autocomplete="off">
                                    <button class="btn btn-primary btn-lg" 
                                            type="button" 
                                            id="analyzeBtn">
                                        <i class="fas fa-search me-2"></i>Analyze
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4 mt-3 mt-md-0">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="includeNearMatches" checked>
                                    <label class="form-check-label" for="includeNearMatches">
                                        Include Near Matches
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Indicator -->
        <div id="loadingIndicator" class="row mb-4" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <h5>Analyzing keyword...</h5>
                        <p class="text-muted">This may take a few moments while we gather data from Etsy</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" style="display: none;">
            <!-- Keyword Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex align-items-center">
                        <h2 id="keywordTitle" class="h4 mb-0 me-3"></h2>
                        <span id="cacheIndicator" class="badge bg-info" style="display: none;">
                            <i class="fas fa-clock me-1"></i>Cached Result
                        </span>
                    </div>
                </div>
            </div>

            <!-- Main Stats Row -->
            <div class="row mb-4">
                <!-- Keyword Statistics -->
                <div class="col-lg-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-bar me-2"></i>Keyword Statistics
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-12 mb-3">
                                    <div class="stat-item">
                                        <i class="fas fa-search text-primary"></i>
                                        <div class="stat-label">Avg. Searches</div>
                                        <div class="stat-value" id="avgSearches">-</div>
                                    </div>
                                </div>
                                <div class="col-12 mb-3">
                                    <div class="stat-item">
                                        <i class="fas fa-mouse-pointer text-success"></i>
                                        <div class="stat-label">Avg. Clicks</div>
                                        <div class="stat-value" id="avgClicks">-</div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="stat-item">
                                        <i class="fas fa-users text-warning"></i>
                                        <div class="stat-label">Competition</div>
                                        <div class="stat-value" id="competition">-</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Difficulty Score Circle -->
                            <div class="text-center mt-4">
                                <div class="difficulty-circle">
                                    <canvas id="difficultyChart" width="120" height="120"></canvas>
                                    <div class="difficulty-text">
                                        <span id="difficultyScore">0</span>%
                                        <small>Difficulty</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search Trends -->
                <div class="col-lg-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line me-2"></i>Search Trends (USA)
                            </h5>
                            <i class="fas fa-info-circle text-muted" data-bs-toggle="tooltip" 
                               title="12-month search volume trend"></i>
                        </div>
                        <div class="card-body">
                            <canvas id="trendsChart" height="200"></canvas>
                            <div class="trend-filters mt-3">
                                <div class="btn-group btn-group-sm w-100" role="group">
                                    <input type="radio" class="btn-check" name="trendFilter" id="trendEtsy" checked>
                                    <label class="btn btn-outline-primary" for="trendEtsy">Etsy</label>
                                    
                                    <input type="radio" class="btn-check" name="trendFilter" id="trendAmazon">
                                    <label class="btn btn-outline-primary" for="trendAmazon">Amazon</label>
                                    
                                    <input type="radio" class="btn-check" name="trendFilter" id="trendEbay">
                                    <label class="btn btn-outline-primary" for="trendEbay">eBay</label>
                                    
                                    <input type="radio" class="btn-check" name="trendFilter" id="trendGoogle">
                                    <label class="btn btn-outline-primary" for="trendGoogle">Google</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Searches by Country -->
                <div class="col-lg-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-globe me-2"></i>Searches by Country
                            </h5>
                            <i class="fas fa-info-circle text-muted" data-bs-toggle="tooltip" 
                               title="Geographic distribution of searches"></i>
                        </div>
                        <div class="card-body">
                            <canvas id="countryChart" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Metrics Row -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-dollar-sign text-success fa-2x mb-2"></i>
                            <h6 class="card-title">Avg. Price</h6>
                            <h4 id="avgPrice" class="text-success">$0.00</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-list text-info fa-2x mb-2"></i>
                            <h6 class="card-title">Total Listings</h6>
                            <h4 id="totalListings" class="text-info">0</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-store text-warning fa-2x mb-2"></i>
                            <h6 class="card-title">Top Sellers</h6>
                            <h4 id="topSellers" class="text-warning">0</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-trending-up text-primary fa-2x mb-2"></i>
                            <h6 class="card-title">Trend</h6>
                            <h4 id="trendDirection" class="text-primary">-</h4>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tabs Section -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <ul class="nav nav-tabs card-header-tabs" id="resultTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="ideas-tab" data-bs-toggle="tab" 
                                            data-bs-target="#ideas" type="button" role="tab">
                                        <i class="fas fa-lightbulb me-2"></i>Keyword Ideas
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="matches-tab" data-bs-toggle="tab" 
                                            data-bs-target="#matches" type="button" role="tab">
                                        <i class="fas fa-search-plus me-2"></i>Near Matches
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="analysis-tab" data-bs-toggle="tab" 
                                            data-bs-target="#analysis" type="button" role="tab">
                                        <i class="fas fa-chart-pie me-2"></i>Search Results Analysis
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="listings-tab" data-bs-toggle="tab" 
                                            data-bs-target="#listings" type="button" role="tab">
                                        <i class="fas fa-list-ul me-2"></i>Top Listings
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="marketplaces-tab" data-bs-toggle="tab" 
                                            data-bs-target="#marketplaces" type="button" role="tab">
                                        <i class="fas fa-shopping-cart me-2"></i>Marketplaces
                                    </button>
                                </li>
                            </ul>
                        </div>
                        <div class="card-body">
                            <div class="tab-content" id="resultTabsContent">
                                <div class="tab-pane fade show active" id="ideas" role="tabpanel">
                                    <div id="keywordIdeas">
                                        <p class="text-muted">Keyword suggestions will appear here...</p>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="matches" role="tabpanel">
                                    <div id="nearMatches">
                                        <p class="text-muted">Near matches will appear here...</p>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="analysis" role="tabpanel">
                                    <div id="searchAnalysis">
                                        <p class="text-muted">Search results analysis will appear here...</p>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="listings" role="tabpanel">
                                    <div id="topListings">
                                        <p class="text-muted">Top listings will appear here...</p>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="marketplaces" role="tabpanel">
                                    <div id="marketplacesData">
                                        <p class="text-muted">Marketplace comparison will appear here...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Section -->
        <div id="errorSection" class="row mb-4" style="display: none;">
            <div class="col-12">
                <div class="alert alert-danger" role="alert">
                    <h4 class="alert-heading">
                        <i class="fas fa-exclamation-triangle me-2"></i>Analysis Failed
                    </h4>
                    <p id="errorMessage">An error occurred while analyzing the keyword.</p>
                    <hr>
                    <p class="mb-0">Please try again with a different keyword or check your internet connection.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
