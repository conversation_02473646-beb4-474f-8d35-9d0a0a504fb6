# Configuration settings for Etsy Keyword Research Tool

import os
from datetime import timedelta

class Config:
    # Flask Configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'etsy-keyword-tool-secret-key-2024'
    DEBUG = True
    
    # Database Configuration
    DATABASE_PATH = 'data/keywords.db'
    
    # Scraping Configuration
    SCRAPING_DELAY_MIN = 5  # Minimum delay between requests (seconds)
    SCRAPING_DELAY_MAX = 10  # Maximum delay between requests (seconds)
    MAX_RETRIES = 3
    REQUEST_TIMEOUT = 30
    
    # User Agent Rotation
    USER_AGENTS = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
    ]
    
    # Etsy URLs
    ETSY_BASE_URL = 'https://www.etsy.com'
    ETSY_SEARCH_URL = 'https://www.etsy.com/search'
    
    # Analysis Configuration
    COMPETITION_THRESHOLDS = {
        'low': 1000,
        'medium': 5000,
        'high': 10000
    }
    
    # Cache Configuration
    CACHE_DURATION = timedelta(hours=24)  # Cache results for 24 hours
    
    # Rate Limiting
    REQUESTS_PER_MINUTE = 10
    REQUESTS_PER_HOUR = 200
