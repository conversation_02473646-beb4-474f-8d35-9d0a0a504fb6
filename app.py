# Main Flask Application for Etsy Keyword Research Tool

from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
import os
import sys

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from src.database import DatabaseManager
from src.scraper import EtsyScraper
from src.analyzer import KeywordAnalyzer

# Initialize Flask app
app = Flask(__name__)
app.config.from_object(Config)
CORS(app)

# Initialize components
db_manager = DatabaseManager()
etsy_scraper = EtsyScraper()
keyword_analyzer = KeywordAnalyzer()

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@app.route('/api/analyze', methods=['POST'])
def analyze_keyword():
    """Analyze a keyword and return metrics"""
    try:
        data = request.get_json()
        keyword = data.get('keyword', '').strip().lower()
        
        if not keyword:
            return jsonify({'error': 'Keyword is required'}), 400
        
        # Check if we have cached data
        cached_result = db_manager.get_cached_result(keyword)
        if cached_result:
            return jsonify(cached_result)
        
        # Scrape Etsy data
        scraping_result = etsy_scraper.scrape_keyword_data(keyword)
        
        if not scraping_result['success']:
            return jsonify({'error': scraping_result['error']}), 500
        
        # Analyze the data
        analysis_result = keyword_analyzer.analyze(keyword, scraping_result['data'])
        
        # Save to database
        db_manager.save_analysis(keyword, analysis_result)
        
        return jsonify(analysis_result)
        
    except Exception as e:
        return jsonify({'error': f'Analysis failed: {str(e)}'}), 500

@app.route('/api/history')
def get_history():
    """Get analysis history"""
    try:
        history = db_manager.get_analysis_history()
        return jsonify(history)
    except Exception as e:
        return jsonify({'error': f'Failed to get history: {str(e)}'}), 500

@app.route('/api/keyword/<keyword>')
def get_keyword_data(keyword):
    """Get specific keyword data"""
    try:
        result = db_manager.get_keyword_data(keyword)
        if result:
            return jsonify(result)
        else:
            return jsonify({'error': 'Keyword not found'}), 404
    except Exception as e:
        return jsonify({'error': f'Failed to get keyword data: {str(e)}'}), 500

if __name__ == '__main__':
    # Create data directory if it doesn't exist
    os.makedirs('data', exist_ok=True)
    
    # Initialize database
    db_manager.initialize_database()
    
    # Run the app
    app.run(debug=True, host='127.0.0.1', port=5000)
