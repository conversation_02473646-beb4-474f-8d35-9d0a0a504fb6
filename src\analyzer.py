# Keyword Analysis Algorithm for Etsy Data

import math
import random
from typing import Dict, List, Any
from datetime import datetime, timedelta
from config import Config

class KeywordAnalyzer:
    def __init__(self):
        self.competition_thresholds = Config.COMPETITION_THRESHOLDS
    
    def analyze(self, keyword: str, scraped_data: Dict[str, Any]) -> Dict[str, Any]:
        """Main analysis function that processes scraped data"""
        
        # Extract basic metrics
        total_results = scraped_data.get('total_results', 0)
        listings = scraped_data.get('listings', [])
        price_data = scraped_data.get('price_data', {})
        seller_data = scraped_data.get('seller_data', {})
        
        # Calculate core metrics
        monthly_searches = self.estimate_monthly_searches(total_results, listings)
        avg_clicks = self.estimate_avg_clicks(monthly_searches, total_results)
        competition_score = self.calculate_competition_score(total_results, seller_data)
        competition_level = self.determine_competition_level(competition_score)
        difficulty_score = self.calculate_difficulty_score(total_results, listings, price_data)
        
        # Generate trend data (simulated for now)
        trend_data = self.generate_trend_data(keyword, monthly_searches)
        
        # Generate country data (simulated)
        country_data = self.generate_country_data()
        
        # Calculate additional metrics
        avg_price = price_data.get('avg_price', 0)
        top_sellers = seller_data.get('unique_sellers', 0)
        trend_direction = self.determine_trend_direction(trend_data)
        
        return {
            'keyword': keyword,
            'monthly_searches': monthly_searches,
            'avg_clicks': avg_clicks,
            'competition_score': competition_score,
            'competition_level': competition_level,
            'avg_price': round(avg_price, 2),
            'total_listings': total_results,
            'top_sellers': top_sellers,
            'difficulty_score': difficulty_score,
            'trend_direction': trend_direction,
            'trend_data': trend_data,
            'country_data': country_data,
            'analysis_date': datetime.now().isoformat(),
            'from_cache': False
        }
    
    def estimate_monthly_searches(self, total_results: int, listings: List[Dict]) -> int:
        """Estimate monthly search volume based on listing data"""
        
        if total_results == 0:
            return 0
        
        # Base calculation factors
        base_factor = 0.1  # Base conversion rate from listings to searches
        
        # Adjust based on listing activity
        if listings:
            avg_sales = sum(listing.get('sales', 0) for listing in listings) / len(listings)
            avg_favorites = sum(listing.get('favorites', 0) for listing in listings) / len(listings)
            
            # Activity multiplier
            activity_multiplier = 1 + (avg_sales * 0.01) + (avg_favorites * 0.005)
        else:
            activity_multiplier = 1
        
        # Competition adjustment
        if total_results > 50000:
            competition_multiplier = 1.5  # High competition = more searches
        elif total_results > 10000:
            competition_multiplier = 1.2
        else:
            competition_multiplier = 0.8
        
        # Calculate estimate
        estimated_searches = int(
            total_results * base_factor * activity_multiplier * competition_multiplier
        )
        
        # Apply reasonable bounds
        estimated_searches = max(10, min(estimated_searches, 100000))
        
        return estimated_searches
    
    def estimate_avg_clicks(self, monthly_searches: int, total_results: int) -> int:
        """Estimate average clicks based on search volume and competition"""
        
        if monthly_searches == 0:
            return 0
        
        # Click-through rate varies by competition
        if total_results > 20000:
            ctr = 0.15  # High competition, lower CTR
        elif total_results > 5000:
            ctr = 0.25  # Medium competition
        else:
            ctr = 0.35  # Low competition, higher CTR
        
        # Calculate clicks with some randomness
        base_clicks = int(monthly_searches * ctr)
        variation = int(base_clicks * 0.1)  # 10% variation
        
        clicks = base_clicks + random.randint(-variation, variation)
        return max(0, clicks)
    
    def calculate_competition_score(self, total_results: int, seller_data: Dict) -> int:
        """Calculate competition score (0-10000)"""
        
        # Base score from total results
        base_score = min(total_results, 10000)
        
        # Adjust for seller concentration
        unique_sellers = seller_data.get('unique_sellers', 0)
        if unique_sellers > 0 and total_results > 0:
            # If many sellers, competition is higher
            seller_ratio = unique_sellers / min(total_results, 100)
            seller_adjustment = seller_ratio * 1000
        else:
            seller_adjustment = 0
        
        competition_score = int(base_score + seller_adjustment)
        return min(competition_score, 10000)
    
    def determine_competition_level(self, competition_score: int) -> str:
        """Determine competition level based on score"""
        
        if competition_score >= self.competition_thresholds['high']:
            return 'High'
        elif competition_score >= self.competition_thresholds['medium']:
            return 'Medium'
        else:
            return 'Low'
    
    def calculate_difficulty_score(self, total_results: int, listings: List[Dict], price_data: Dict) -> int:
        """Calculate keyword difficulty score (0-100)"""
        
        # Base difficulty from competition
        competition_factor = min(total_results / 1000, 50)  # Max 50 points
        
        # Price factor (higher prices = more established market = harder)
        avg_price = price_data.get('avg_price', 0)
        if avg_price > 100:
            price_factor = 20
        elif avg_price > 50:
            price_factor = 15
        elif avg_price > 20:
            price_factor = 10
        else:
            price_factor = 5
        
        # Sales activity factor
        if listings:
            avg_sales = sum(listing.get('sales', 0) for listing in listings) / len(listings)
            if avg_sales > 100:
                sales_factor = 20
            elif avg_sales > 50:
                sales_factor = 15
            elif avg_sales > 10:
                sales_factor = 10
            else:
                sales_factor = 5
        else:
            sales_factor = 5
        
        # Rating factor (high ratings = established sellers = harder)
        if listings:
            avg_rating = sum(listing.get('rating', 0) for listing in listings if listing.get('rating', 0) > 0)
            if avg_rating > 0:
                avg_rating = avg_rating / len([l for l in listings if l.get('rating', 0) > 0])
                if avg_rating > 4.5:
                    rating_factor = 10
                elif avg_rating > 4.0:
                    rating_factor = 7
                else:
                    rating_factor = 5
            else:
                rating_factor = 5
        else:
            rating_factor = 5
        
        total_difficulty = int(competition_factor + price_factor + sales_factor + rating_factor)
        return min(total_difficulty, 100)
    
    def generate_trend_data(self, keyword: str, monthly_searches: int) -> List[int]:
        """Generate simulated 12-month trend data"""
        
        # Create a realistic trend pattern
        base_value = monthly_searches
        trend_data = []
        
        # Generate seasonal pattern
        seasonal_multipliers = [0.8, 0.85, 0.9, 0.95, 1.0, 1.05, 1.1, 1.15, 1.1, 1.0, 0.95, 0.9]
        
        for i in range(12):
            # Apply seasonal variation
            seasonal_value = base_value * seasonal_multipliers[i]
            
            # Add some randomness
            variation = random.uniform(0.85, 1.15)
            final_value = int(seasonal_value * variation)
            
            trend_data.append(max(0, final_value))
        
        return trend_data
    
    def determine_trend_direction(self, trend_data: List[int]) -> str:
        """Determine overall trend direction"""
        
        if len(trend_data) < 2:
            return 'Stable'
        
        # Compare recent months with earlier months
        recent_avg = sum(trend_data[-3:]) / 3
        earlier_avg = sum(trend_data[:3]) / 3
        
        change_percent = (recent_avg - earlier_avg) / earlier_avg if earlier_avg > 0 else 0
        
        if change_percent > 0.1:
            return 'Rising'
        elif change_percent < -0.1:
            return 'Declining'
        else:
            return 'Stable'
    
    def generate_country_data(self) -> Dict[str, float]:
        """Generate simulated country distribution data"""
        
        # Simulate realistic country distribution for Etsy
        countries = {
            'United States': random.uniform(40, 60),
            'United Kingdom': random.uniform(8, 15),
            'Canada': random.uniform(5, 12),
            'Australia': random.uniform(3, 8),
            'Germany': random.uniform(3, 7),
            'France': random.uniform(2, 6),
            'Other': 0
        }
        
        # Normalize to 100%
        total = sum(countries.values())
        for country in countries:
            if country != 'Other':
                countries[country] = round((countries[country] / total) * 100, 1)
        
        countries['Other'] = round(100 - sum(v for k, v in countries.items() if k != 'Other'), 1)
        
        return countries
    
    def get_keyword_suggestions(self, keyword: str, scraped_data: Dict) -> List[str]:
        """Generate keyword suggestions based on scraped data"""
        
        suggestions = []
        listings = scraped_data.get('listings', [])
        
        # Extract words from listing titles
        words = set()
        for listing in listings:
            title = listing.get('title', '').lower()
            title_words = title.split()
            for word in title_words:
                if len(word) > 3 and word.isalpha():
                    words.add(word)
        
        # Create combinations with original keyword
        base_words = keyword.split()
        for word in list(words)[:10]:  # Limit to 10 suggestions
            if word not in base_words:
                suggestions.append(f"{keyword} {word}")
                if len(base_words) == 1:
                    suggestions.append(f"{word} {keyword}")
        
        return suggestions[:8]  # Return top 8 suggestions
