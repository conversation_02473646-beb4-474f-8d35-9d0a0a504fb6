# Etsy Keyword Research Tool

A local keyword research tool specifically designed for Etsy sellers to analyze keyword performance, competition, and search trends.

## Features

- **Keyword Analysis**: Get monthly search estimates, competition levels, and difficulty scores
- **Visual Analytics**: Interactive charts showing search trends and geographic distribution
- **Competition Analysis**: Understand market competition and pricing
- **Local Database**: SQLite database for caching results and history
- **Respectful Scraping**: Rate-limited scraping with proper delays
- **Modern UI**: Clean, responsive interface similar to professional SEO tools

## Installation

1. **Clone or download the project**
   ```bash
   cd etsy-keyword-tool
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python app.py
   ```

4. **Open your browser**
   Navigate to: `http://localhost:5000`

## Usage

1. Enter a keyword in the search box (e.g., "handmade jewelry")
2. Click "Analyze" or press Enter
3. Wait for the analysis to complete (may take 10-30 seconds)
4. Review the results:
   - **Monthly search estimates**
   - **Competition level and difficulty score**
   - **Average pricing information**
   - **Search trends over 12 months**
   - **Geographic distribution**

## How It Works

### Data Collection
- Scrapes Etsy search results for the given keyword
- Extracts listing counts, pricing, sales data, and seller information
- Respects rate limits with 2-5 second delays between requests

### Analysis Algorithm
- **Monthly Searches**: Estimated based on total listings, sales activity, and competition
- **Competition Score**: Calculated from total results and seller concentration
- **Difficulty Score**: Based on competition, pricing, sales activity, and seller ratings
- **Trends**: Simulated 12-month trend data with seasonal variations

### Caching
- Results are cached for 24 hours to avoid repeated scraping
- SQLite database stores analysis history and cached results

## Project Structure

```
etsy-keyword-tool/
├── app.py                 # Main Flask application
├── config.py              # Configuration settings
├── requirements.txt       # Python dependencies
├── src/
│   ├── __init__.py
│   ├── database.py        # Database operations
│   ├── scraper.py         # Etsy scraping module
│   └── analyzer.py        # Keyword analysis algorithm
├── templates/
│   └── index.html         # Main HTML template
├── static/
│   ├── css/
│   │   └── style.css      # Custom styles
│   └── js/
│       └── app.js         # Frontend JavaScript
└── data/
    └── keywords.db        # SQLite database (created automatically)
```

## Configuration

Edit `config.py` to modify:
- Scraping delays and timeouts
- Rate limiting settings
- Cache duration
- Competition thresholds

## Important Notes

### Legal Considerations
- This tool is for personal use only
- Respects Etsy's robots.txt and rate limits
- Uses public data only
- Not for commercial redistribution

### Limitations
- Search volume estimates are algorithmic approximations
- Etsy doesn't provide official search volume data
- Results may vary based on market conditions
- Scraping may occasionally fail due to site changes

### Performance
- First analysis of a keyword takes 10-30 seconds
- Cached results load instantly
- Database grows over time (periodic cleanup recommended)

## Troubleshooting

### Common Issues

1. **"Analysis Failed" Error**
   - Check internet connection
   - Try a different keyword
   - Wait a few minutes and retry (may be rate limited)

2. **Slow Performance**
   - Normal for first-time keyword analysis
   - Subsequent analyses use cached data

3. **No Results Found**
   - Keyword may be too specific or uncommon
   - Try broader or more popular keywords

### Rate Limiting
If you encounter rate limiting:
- Wait 5-10 minutes before trying again
- Reduce analysis frequency
- Consider using a VPN if needed

## Development

### Adding Features
- Modify `src/analyzer.py` for new analysis metrics
- Update `templates/index.html` and `static/js/app.js` for UI changes
- Add new API endpoints in `app.py`

### Database Schema
The SQLite database includes tables for:
- `keyword_analysis`: Main analysis results
- `search_history`: User search logs
- `scraping_cache`: Cached scraping data

## Disclaimer

This tool is for educational and personal use only. The search volume estimates are algorithmic approximations and should not be considered as official Etsy data. Always verify important business decisions with multiple data sources.

## License

This project is for personal use only. Not licensed for commercial distribution or resale.
