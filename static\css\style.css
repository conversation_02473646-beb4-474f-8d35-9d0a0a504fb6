/* Custom CSS for Etsy Keyword Research Tool */

:root {
    --primary-color: #0066cc;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

body {
    background-color: #f5f5f5;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header Styles */
header {
    background: linear-gradient(135deg, var(--primary-color), #004499) !important;
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: 12px;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
    border-bottom: 1px solid #e9ecef;
}

/* Statistics Items */
.stat-item {
    padding: 15px 0;
}

.stat-item i {
    font-size: 1.5rem;
    margin-bottom: 8px;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--dark-color);
}

/* Difficulty Circle */
.difficulty-circle {
    position: relative;
    display: inline-block;
    margin: 20px 0;
}

.difficulty-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    font-weight: bold;
}

.difficulty-text span {
    font-size: 1.5rem;
    color: var(--primary-color);
    display: block;
}

.difficulty-text small {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Chart Containers */
.chart-container {
    position: relative;
    height: 250px;
    margin: 20px 0;
}

/* Trend Filters */
.trend-filters .btn-group {
    border-radius: 8px;
    overflow: hidden;
}

.trend-filters .btn {
    border-radius: 0;
    font-size: 0.85rem;
}

/* Input Enhancements */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 102, 204, 0.25);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

/* Loading Animation */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Tab Enhancements */
.nav-tabs .nav-link {
    border-radius: 8px 8px 0 0;
    margin-right: 5px;
    color: #6c757d;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef var(--primary-color);
    color: var(--primary-color);
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    background-color: #fff;
    border-color: var(--primary-color) var(--primary-color) #fff;
}

/* Metric Cards */
.metric-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.metric-card:hover {
    background-color: var(--light-color);
}

/* Badge Enhancements */
.badge {
    font-size: 0.8rem;
    padding: 0.5em 0.8em;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .stat-value {
        font-size: 1.5rem;
    }
    
    .difficulty-text span {
        font-size: 1.2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .trend-filters .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Competition Level Colors */
.competition-low {
    color: var(--success-color) !important;
}

.competition-medium {
    color: var(--warning-color) !important;
}

.competition-high {
    color: var(--danger-color) !important;
}

/* Trend Direction Colors */
.trend-rising {
    color: var(--success-color) !important;
}

.trend-stable {
    color: var(--info-color) !important;
}

.trend-declining {
    color: var(--danger-color) !important;
}

/* Tooltip Enhancements */
.tooltip {
    font-size: 0.875rem;
}

/* Table Enhancements */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
    background-color: var(--light-color);
}

.table td {
    vertical-align: middle;
}

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: 4px;
}

.progress-bar {
    border-radius: 4px;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Error Styles */
.alert {
    border-radius: 12px;
    border: none;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* Success Styles */
.alert-success {
    background-color: #d4edda;
    color: #155724;
}
