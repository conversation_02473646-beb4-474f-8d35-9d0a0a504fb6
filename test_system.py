#!/usr/bin/env python3
"""
Test script for Etsy Keyword Research Tool
Tests basic functionality without making actual web requests
"""

import sys
import os
import sqlite3
import json
from datetime import datetime

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from database import DatabaseManager
from analyzer import KeywordAnalyzer

def test_database():
    """Test database functionality"""
    print("Testing database functionality...")
    
    try:
        # Initialize database
        db = DatabaseManager()
        db.initialize_database()
        
        # Test data
        test_analysis = {
            'keyword': 'test keyword',
            'monthly_searches': 1500,
            'avg_clicks': 450,
            'competition_score': 3500,
            'competition_level': 'Medium',
            'avg_price': 25.99,
            'total_listings': 8500,
            'top_sellers': 125,
            'difficulty_score': 65,
            'trend_direction': 'Rising',
            'trend_data': [100, 120, 110, 130, 140, 135, 150, 145, 160, 155, 170, 165],
            'country_data': {
                'United States': 45.2,
                'United Kingdom': 12.8,
                'Canada': 8.5,
                'Australia': 6.1,
                'Germany': 4.7,
                'France': 3.9,
                'Other': 18.8
            }
        }
        
        # Save analysis
        success = db.save_analysis('test keyword', test_analysis)
        if success:
            print("✓ Database save test passed")
        else:
            print("✗ Database save test failed")
            return False
        
        # Retrieve analysis
        retrieved = db.get_keyword_data('test keyword')
        if retrieved and retrieved['keyword'] == 'test keyword':
            print("✓ Database retrieve test passed")
        else:
            print("✗ Database retrieve test failed")
            return False
        
        # Test history
        history = db.get_analysis_history(5)
        if isinstance(history, list):
            print("✓ Database history test passed")
        else:
            print("✗ Database history test failed")
            return False
        
        print("✓ All database tests passed")
        return True
        
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        return False

def test_analyzer():
    """Test keyword analyzer functionality"""
    print("\nTesting keyword analyzer...")
    
    try:
        analyzer = KeywordAnalyzer()
        
        # Mock scraped data
        mock_scraped_data = {
            'total_results': 5000,
            'listings': [
                {
                    'title': 'Handmade Silver Ring',
                    'price': 45.99,
                    'sales': 25,
                    'rating': 4.8,
                    'favorites': 12,
                    'shop_name': 'SilverCrafts'
                },
                {
                    'title': 'Custom Wedding Ring',
                    'price': 89.99,
                    'sales': 45,
                    'rating': 4.9,
                    'favorites': 28,
                    'shop_name': 'WeddingJewelry'
                }
            ],
            'price_data': {
                'min_price': 15.99,
                'max_price': 199.99,
                'avg_price': 67.99,
                'price_count': 48
            },
            'seller_data': {
                'unique_sellers': 35,
                'total_shop_links': 48
            },
            'pagination_info': {
                'current_page': 1,
                'total_pages': 25,
                'has_next': True
            }
        }
        
        # Test analysis
        result = analyzer.analyze('silver ring', mock_scraped_data)
        
        # Validate result structure
        required_fields = [
            'keyword', 'monthly_searches', 'avg_clicks', 'competition_score',
            'competition_level', 'avg_price', 'total_listings', 'top_sellers',
            'difficulty_score', 'trend_direction', 'trend_data', 'country_data'
        ]
        
        for field in required_fields:
            if field not in result:
                print(f"✗ Missing field in analysis result: {field}")
                return False
        
        # Validate data types and ranges
        if not isinstance(result['monthly_searches'], int) or result['monthly_searches'] < 0:
            print("✗ Invalid monthly_searches value")
            return False
        
        if result['competition_level'] not in ['Low', 'Medium', 'High']:
            print("✗ Invalid competition_level value")
            return False
        
        if not (0 <= result['difficulty_score'] <= 100):
            print("✗ Invalid difficulty_score range")
            return False
        
        if len(result['trend_data']) != 12:
            print("✗ Invalid trend_data length")
            return False
        
        print("✓ Keyword analyzer test passed")
        print(f"  - Monthly searches: {result['monthly_searches']}")
        print(f"  - Competition: {result['competition_level']} ({result['competition_score']})")
        print(f"  - Difficulty: {result['difficulty_score']}%")
        print(f"  - Trend: {result['trend_direction']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Analyzer test failed: {e}")
        return False

def test_config():
    """Test configuration loading"""
    print("\nTesting configuration...")
    
    try:
        from config import Config
        
        # Test required config attributes
        required_attrs = [
            'DATABASE_PATH', 'SCRAPING_DELAY_MIN', 'SCRAPING_DELAY_MAX',
            'USER_AGENTS', 'ETSY_BASE_URL', 'COMPETITION_THRESHOLDS'
        ]
        
        for attr in required_attrs:
            if not hasattr(Config, attr):
                print(f"✗ Missing config attribute: {attr}")
                return False
        
        # Test config values
        if Config.SCRAPING_DELAY_MIN >= Config.SCRAPING_DELAY_MAX:
            print("✗ Invalid scraping delay configuration")
            return False
        
        if not isinstance(Config.USER_AGENTS, list) or len(Config.USER_AGENTS) == 0:
            print("✗ Invalid user agents configuration")
            return False
        
        print("✓ Configuration test passed")
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_file_structure():
    """Test that all required files exist"""
    print("\nTesting file structure...")
    
    required_files = [
        'app.py',
        'config.py',
        'requirements.txt',
        'src/__init__.py',
        'src/database.py',
        'src/scraper.py',
        'src/analyzer.py',
        'templates/index.html',
        'static/css/style.css',
        'static/js/app.js'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("✗ Missing files:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    print("✓ File structure test passed")
    return True

def main():
    """Run all tests"""
    print("Etsy Keyword Research Tool - System Test")
    print("=" * 50)
    
    tests = [
        test_file_structure,
        test_config,
        test_database,
        test_analyzer
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! System is ready to use.")
        print("\nTo start the application:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Run the app: python app.py")
        print("3. Open browser: http://localhost:5000")
    else:
        print("✗ Some tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
