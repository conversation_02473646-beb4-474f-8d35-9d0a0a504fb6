#!/usr/bin/env python3
"""
ChromeDriver Installation Script for Etsy Scraper
Automatically downloads and installs ChromeDriver
"""

import os
import sys
import requests
import zipfile
import platform
import subprocess
from pathlib import Path

def get_chrome_version():
    """Get installed Chrome version"""
    try:
        if platform.system() == "Windows":
            # Try different Chrome installation paths
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
            ]
            
            for chrome_path in chrome_paths:
                if os.path.exists(chrome_path):
                    # Get version using PowerShell
                    cmd = f'powershell "(Get-Item \'{chrome_path}\').VersionInfo.ProductVersion"'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                    if result.returncode == 0:
                        version = result.stdout.strip()
                        return version.split('.')[0]  # Return major version
            
        elif platform.system() == "Darwin":  # macOS
            cmd = "/Applications/Google\\ Chrome.app/Contents/MacOS/Google\\ Chrome --version"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                version = result.stdout.strip().split()[-1]
                return version.split('.')[0]
                
        elif platform.system() == "Linux":
            cmd = "google-chrome --version"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                version = result.stdout.strip().split()[-1]
                return version.split('.')[0]
                
    except Exception as e:
        print(f"Error getting Chrome version: {e}")
    
    return None

def download_chromedriver(version):
    """Download ChromeDriver for the specified version"""
    try:
        # Try new Chrome for Testing API first
        if int(version) >= 115:
            return download_chromedriver_new_api(version)

        # Fallback to old API for older versions
        base_url = "https://chromedriver.storage.googleapis.com"

        # Get the latest version for the major Chrome version
        version_url = f"{base_url}/LATEST_RELEASE_{version}"
        response = requests.get(version_url)

        if response.status_code != 200:
            print(f"Could not find ChromeDriver for Chrome version {version}")
            return False

        chromedriver_version = response.text.strip()
        print(f"Downloading ChromeDriver version {chromedriver_version}")

        # Determine platform
        if platform.system() == "Windows":
            platform_name = "win32"
            executable_name = "chromedriver.exe"
        elif platform.system() == "Darwin":
            platform_name = "mac64"
            executable_name = "chromedriver"
        else:
            platform_name = "linux64"
            executable_name = "chromedriver"

        # Download URL
        download_url = f"{base_url}/{chromedriver_version}/chromedriver_{platform_name}.zip"

        print(f"Downloading from: {download_url}")
        response = requests.get(download_url)

        if response.status_code != 200:
            print("Failed to download ChromeDriver")
            return False

        # Save and extract
        zip_path = "chromedriver.zip"
        with open(zip_path, "wb") as f:
            f.write(response.content)

        # Extract
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(".")

        # Clean up
        os.remove(zip_path)

        # Make executable on Unix systems
        if platform.system() != "Windows":
            os.chmod(executable_name, 0o755)

        print(f"ChromeDriver installed successfully: {executable_name}")
        return True

    except Exception as e:
        print(f"Error downloading ChromeDriver: {e}")
        return False

def download_chromedriver_new_api(version):
    """Download ChromeDriver using new Chrome for Testing API"""
    try:
        # New API endpoint
        api_url = "https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json"

        print("Fetching available ChromeDriver versions...")
        response = requests.get(api_url)

        if response.status_code != 200:
            print("Failed to fetch ChromeDriver versions")
            return False

        data = response.json()

        # Find the latest version for the major Chrome version
        target_version = None
        for version_info in reversed(data['versions']):
            if version_info['version'].startswith(f"{version}."):
                if 'chromedriver' in version_info.get('downloads', {}):
                    target_version = version_info
                    break

        if not target_version:
            print(f"No ChromeDriver found for Chrome version {version}")
            return False

        # Determine platform
        if platform.system() == "Windows":
            platform_name = "win64"
            executable_name = "chromedriver.exe"
        elif platform.system() == "Darwin":
            platform_name = "mac-x64"
            executable_name = "chromedriver"
        else:
            platform_name = "linux64"
            executable_name = "chromedriver"

        # Find download URL for our platform
        chromedriver_downloads = target_version['downloads']['chromedriver']
        download_url = None

        for download in chromedriver_downloads:
            if download['platform'] == platform_name:
                download_url = download['url']
                break

        if not download_url:
            print(f"No ChromeDriver download found for platform {platform_name}")
            return False

        print(f"Downloading ChromeDriver {target_version['version']} for {platform_name}")
        print(f"URL: {download_url}")

        response = requests.get(download_url)
        if response.status_code != 200:
            print("Failed to download ChromeDriver")
            return False

        # Save and extract
        zip_path = "chromedriver.zip"
        with open(zip_path, "wb") as f:
            f.write(response.content)

        # Extract
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(".")

        # Clean up
        os.remove(zip_path)

        # Find the extracted chromedriver executable
        for root, dirs, files in os.walk("."):
            for file in files:
                if file == executable_name:
                    src_path = os.path.join(root, file)
                    if src_path != f"./{executable_name}":
                        # Move to current directory
                        os.rename(src_path, executable_name)
                    break

        # Make executable on Unix systems
        if platform.system() != "Windows":
            os.chmod(executable_name, 0o755)

        # Clean up extracted folders
        for item in os.listdir("."):
            if os.path.isdir(item) and item.startswith("chromedriver"):
                import shutil
                shutil.rmtree(item)

        print(f"ChromeDriver installed successfully: {executable_name}")
        return True

    except Exception as e:
        print(f"Error downloading ChromeDriver with new API: {e}")
        return False

def check_chromedriver():
    """Check if ChromeDriver is already available"""
    try:
        result = subprocess.run(["chromedriver", "--version"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("ChromeDriver is already installed and available in PATH")
            return True
    except:
        pass
    
    # Check in current directory
    if platform.system() == "Windows":
        if os.path.exists("chromedriver.exe"):
            print("ChromeDriver found in current directory")
            return True
    else:
        if os.path.exists("chromedriver"):
            print("ChromeDriver found in current directory")
            return True
    
    return False

def main():
    print("ChromeDriver Installation for Etsy Scraper")
    print("=" * 50)
    
    # Check if ChromeDriver is already available
    if check_chromedriver():
        print("✓ ChromeDriver is ready to use")
        return
    
    # Get Chrome version
    chrome_version = get_chrome_version()
    if not chrome_version:
        print("❌ Google Chrome not found!")
        print("\nPlease install Google Chrome first:")
        print("https://www.google.com/chrome/")
        return
    
    print(f"✓ Found Chrome version: {chrome_version}")
    
    # Download ChromeDriver
    if download_chromedriver(chrome_version):
        print("✓ ChromeDriver installation completed!")
        print("\nYou can now use the Etsy scraper with real data.")
    else:
        print("❌ ChromeDriver installation failed!")
        print("\nManual installation:")
        print("1. Go to: https://chromedriver.chromium.org/")
        print("2. Download ChromeDriver for your Chrome version")
        print("3. Extract and place chromedriver.exe in this folder")

if __name__ == "__main__":
    main()
