// Main JavaScript for Etsy Keyword Research Tool

class KeywordAnalyzer {
    constructor() {
        this.charts = {};
        this.currentKeyword = '';
        this.initializeEventListeners();
        this.initializeTooltips();
    }

    initializeEventListeners() {
        // Search button click
        document.getElementById('analyzeBtn').addEventListener('click', () => {
            this.analyzeKeyword();
        });

        // Enter key in search input
        document.getElementById('keywordInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.analyzeKeyword();
            }
        });

        // Trend filter buttons
        document.querySelectorAll('input[name="trendFilter"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.updateTrendChart(e.target.id);
            });
        });
    }

    initializeTooltips() {
        // Initialize Bootstrap tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    async analyzeKeyword() {
        const keyword = document.getElementById('keywordInput').value.trim();
        
        if (!keyword) {
            this.showError('Please enter a keyword to analyze');
            return;
        }

        this.currentKeyword = keyword;
        this.showLoading();
        this.hideError();
        this.hideResults();

        try {
            const response = await fetch('/api/analyze', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ keyword: keyword })
            });

            const data = await response.json();

            if (response.ok) {
                this.displayResults(data);
            } else {
                this.showError(data.error || 'Analysis failed');
            }
        } catch (error) {
            console.error('Error:', error);
            this.showError('Network error. Please check your connection and try again.');
        } finally {
            this.hideLoading();
        }
    }

    showLoading() {
        document.getElementById('loadingIndicator').style.display = 'block';
    }

    hideLoading() {
        document.getElementById('loadingIndicator').style.display = 'none';
    }

    showError(message) {
        document.getElementById('errorMessage').textContent = message;
        document.getElementById('errorSection').style.display = 'block';
    }

    hideError() {
        document.getElementById('errorSection').style.display = 'none';
    }

    hideResults() {
        document.getElementById('resultsSection').style.display = 'none';
    }

    displayResults(data) {
        // Update keyword title
        document.getElementById('keywordTitle').textContent = data.keyword;
        
        // Show cache indicator if applicable
        const cacheIndicator = document.getElementById('cacheIndicator');
        if (data.from_cache) {
            cacheIndicator.style.display = 'inline-block';
        } else {
            cacheIndicator.style.display = 'none';
        }

        // Update main statistics
        this.updateStatistics(data);
        
        // Update additional metrics
        this.updateAdditionalMetrics(data);
        
        // Create charts
        this.createDifficultyChart(data.difficulty_score);
        this.createTrendChart(data.trend_data);
        this.createCountryChart(data.country_data);
        
        // Update tabs content
        this.updateTabsContent(data);
        
        // Show results with animation
        const resultsSection = document.getElementById('resultsSection');
        resultsSection.style.display = 'block';
        resultsSection.classList.add('fade-in');
    }

    updateStatistics(data) {
        document.getElementById('avgSearches').textContent = this.formatNumber(data.monthly_searches);
        document.getElementById('avgClicks').textContent = this.formatNumber(data.avg_clicks);
        document.getElementById('competition').textContent = this.formatNumber(data.competition_score);
        
        // Apply competition level styling
        const competitionElement = document.getElementById('competition');
        competitionElement.className = `stat-value competition-${data.competition_level.toLowerCase()}`;
    }

    updateAdditionalMetrics(data) {
        document.getElementById('avgPrice').textContent = `$${data.avg_price.toFixed(2)}`;
        document.getElementById('totalListings').textContent = this.formatNumber(data.total_listings);
        document.getElementById('topSellers').textContent = this.formatNumber(data.top_sellers);
        
        const trendElement = document.getElementById('trendDirection');
        trendElement.textContent = data.trend_direction;
        trendElement.className = `text-primary trend-${data.trend_direction.toLowerCase()}`;
    }

    createDifficultyChart(score) {
        const ctx = document.getElementById('difficultyChart').getContext('2d');
        
        // Destroy existing chart if it exists
        if (this.charts.difficulty) {
            this.charts.difficulty.destroy();
        }

        // Update difficulty score text
        document.getElementById('difficultyScore').textContent = score;

        // Determine color based on difficulty
        let color;
        if (score < 30) {
            color = '#28a745'; // Green
        } else if (score < 70) {
            color = '#ffc107'; // Yellow
        } else {
            color = '#dc3545'; // Red
        }

        this.charts.difficulty = new Chart(ctx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [score, 100 - score],
                    backgroundColor: [color, '#e9ecef'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: false,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    }
                }
            }
        });
    }

    createTrendChart(trendData) {
        const ctx = document.getElementById('trendsChart').getContext('2d');
        
        // Destroy existing chart if it exists
        if (this.charts.trends) {
            this.charts.trends.destroy();
        }

        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
                       'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

        this.charts.trends = new Chart(ctx, {
            type: 'line',
            data: {
                labels: months,
                datasets: [{
                    label: 'Search Volume',
                    data: trendData,
                    borderColor: '#0066cc',
                    backgroundColor: 'rgba(0, 102, 204, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#0066cc',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#e9ecef'
                        },
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString();
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                elements: {
                    point: {
                        hoverRadius: 8
                    }
                }
            }
        });
    }

    createCountryChart(countryData) {
        const ctx = document.getElementById('countryChart').getContext('2d');
        
        // Destroy existing chart if it exists
        if (this.charts.country) {
            this.charts.country.destroy();
        }

        const countries = Object.keys(countryData);
        const values = Object.values(countryData);
        
        const colors = [
            '#0066cc', '#28a745', '#ffc107', '#dc3545', 
            '#17a2b8', '#6f42c1', '#fd7e14'
        ];

        this.charts.country = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: countries,
                datasets: [{
                    data: values,
                    backgroundColor: colors,
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true,
                            font: {
                                size: 11
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed + '%';
                            }
                        }
                    }
                }
            }
        });
    }

    updateTrendChart(filterId) {
        // This would update the trend chart based on the selected marketplace
        // For now, we'll just show the same data
        console.log('Trend filter changed to:', filterId);
    }

    updateTabsContent(data) {
        // Update Keyword Ideas tab
        this.updateKeywordIdeas(data);
        
        // Update Near Matches tab
        this.updateNearMatches(data);
        
        // Update Search Results Analysis tab
        this.updateSearchAnalysis(data);
        
        // Update Top Listings tab
        this.updateTopListings(data);
        
        // Update Marketplaces tab
        this.updateMarketplaces(data);
    }

    updateKeywordIdeas(data) {
        const container = document.getElementById('keywordIdeas');
        
        // Generate some keyword suggestions based on the main keyword
        const suggestions = this.generateKeywordSuggestions(data.keyword);
        
        let html = '<div class="row">';
        suggestions.forEach((suggestion, index) => {
            html += `
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">${suggestion.keyword}</h6>
                            <div class="row text-center">
                                <div class="col-4">
                                    <small class="text-muted">Volume</small>
                                    <div class="fw-bold">${this.formatNumber(suggestion.volume)}</div>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">Competition</small>
                                    <div class="fw-bold">${suggestion.competition}</div>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">Difficulty</small>
                                    <div class="fw-bold">${suggestion.difficulty}%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';
        
        container.innerHTML = html;
    }

    updateNearMatches(data) {
        const container = document.getElementById('nearMatches');
        container.innerHTML = '<p class="text-muted">Near matches feature coming soon...</p>';
    }

    updateSearchAnalysis(data) {
        const container = document.getElementById('searchAnalysis');
        
        const html = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Market Overview</h6>
                    <ul class="list-unstyled">
                        <li><strong>Total Results:</strong> ${this.formatNumber(data.total_listings)}</li>
                        <li><strong>Competition Level:</strong> <span class="competition-${data.competition_level.toLowerCase()}">${data.competition_level}</span></li>
                        <li><strong>Average Price:</strong> $${data.avg_price.toFixed(2)}</li>
                        <li><strong>Market Trend:</strong> <span class="trend-${data.trend_direction.toLowerCase()}">${data.trend_direction}</span></li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Opportunity Score</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar" role="progressbar" style="width: ${100 - data.difficulty_score}%" 
                             aria-valuenow="${100 - data.difficulty_score}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <small class="text-muted">Based on competition and search volume</small>
                </div>
            </div>
        `;
        
        container.innerHTML = html;
    }

    updateTopListings(data) {
        const container = document.getElementById('topListings');
        container.innerHTML = '<p class="text-muted">Top listings analysis coming soon...</p>';
    }

    updateMarketplaces(data) {
        const container = document.getElementById('marketplacesData');
        
        const html = `
            <div class="row">
                <div class="col-md-3 text-center mb-3">
                    <div class="card">
                        <div class="card-body">
                            <i class="fab fa-etsy fa-2x text-primary mb-2"></i>
                            <h6>Etsy</h6>
                            <div class="fw-bold text-success">Active</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 text-center mb-3">
                    <div class="card">
                        <div class="card-body">
                            <i class="fab fa-amazon fa-2x text-warning mb-2"></i>
                            <h6>Amazon</h6>
                            <div class="fw-bold text-muted">Coming Soon</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 text-center mb-3">
                    <div class="card">
                        <div class="card-body">
                            <i class="fab fa-ebay fa-2x text-info mb-2"></i>
                            <h6>eBay</h6>
                            <div class="fw-bold text-muted">Coming Soon</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 text-center mb-3">
                    <div class="card">
                        <div class="card-body">
                            <i class="fas fa-shopping-cart fa-2x text-secondary mb-2"></i>
                            <h6>Shopify</h6>
                            <div class="fw-bold text-muted">Coming Soon</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        container.innerHTML = html;
    }

    generateKeywordSuggestions(keyword) {
        // Generate some realistic keyword suggestions
        const modifiers = ['handmade', 'vintage', 'custom', 'personalized', 'unique', 'artisan'];
        const suffixes = ['gift', 'set', 'collection', 'design', 'style', 'decor'];
        
        const suggestions = [];
        const baseVolume = Math.floor(Math.random() * 1000) + 100;
        
        for (let i = 0; i < 6; i++) {
            let suggestedKeyword;
            if (i < 3) {
                suggestedKeyword = `${modifiers[i]} ${keyword}`;
            } else {
                suggestedKeyword = `${keyword} ${suffixes[i - 3]}`;
            }
            
            suggestions.push({
                keyword: suggestedKeyword,
                volume: Math.floor(baseVolume * (0.5 + Math.random() * 0.8)),
                competition: ['Low', 'Medium', 'High'][Math.floor(Math.random() * 3)],
                difficulty: Math.floor(Math.random() * 100)
            });
        }
        
        return suggestions;
    }

    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new KeywordAnalyzer();
});
