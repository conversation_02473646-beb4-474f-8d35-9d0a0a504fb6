# Database Manager for Etsy Keyword Research Tool

import sqlite3
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from config import Config

class DatabaseManager:
    def __init__(self):
        self.db_path = Config.DATABASE_PATH
        self.ensure_data_directory()
    
    def ensure_data_directory(self):
        """Create data directory if it doesn't exist"""
        data_dir = os.path.dirname(self.db_path)
        if data_dir and not os.path.exists(data_dir):
            os.makedirs(data_dir)
    
    def get_connection(self):
        """Get database connection"""
        return sqlite3.connect(self.db_path)
    
    def initialize_database(self):
        """Initialize database with required tables"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Keywords analysis table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS keyword_analysis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    keyword TEXT NOT NULL UNIQUE,
                    monthly_searches INTEGER,
                    avg_clicks INTEGER,
                    competition_score INTEGER,
                    competition_level TEXT,
                    avg_price REAL,
                    total_listings INTEGER,
                    top_sellers INTEGER,
                    difficulty_score INTEGER,
                    trend_direction TEXT,
                    trend_data TEXT,
                    country_data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Search history table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS search_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    keyword TEXT NOT NULL,
                    search_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    user_ip TEXT,
                    response_time REAL
                )
            ''')
            
            # Cached scraping data table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS scraping_cache (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    keyword TEXT NOT NULL UNIQUE,
                    raw_data TEXT,
                    cached_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP
                )
            ''')
            
            conn.commit()
    
    def save_analysis(self, keyword: str, analysis_data: Dict[str, Any]) -> bool:
        """Save keyword analysis to database"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Convert complex data to JSON strings
                trend_data_json = json.dumps(analysis_data.get('trend_data', []))
                country_data_json = json.dumps(analysis_data.get('country_data', {}))
                
                cursor.execute('''
                    INSERT OR REPLACE INTO keyword_analysis 
                    (keyword, monthly_searches, avg_clicks, competition_score, 
                     competition_level, avg_price, total_listings, top_sellers,
                     difficulty_score, trend_direction, trend_data, country_data, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    keyword,
                    analysis_data.get('monthly_searches', 0),
                    analysis_data.get('avg_clicks', 0),
                    analysis_data.get('competition_score', 0),
                    analysis_data.get('competition_level', 'Unknown'),
                    analysis_data.get('avg_price', 0.0),
                    analysis_data.get('total_listings', 0),
                    analysis_data.get('top_sellers', 0),
                    analysis_data.get('difficulty_score', 0),
                    analysis_data.get('trend_direction', 'Stable'),
                    trend_data_json,
                    country_data_json,
                    datetime.now()
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"Error saving analysis: {e}")
            return False
    
    def get_keyword_data(self, keyword: str) -> Optional[Dict[str, Any]]:
        """Get keyword analysis data"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM keyword_analysis WHERE keyword = ?
                ''', (keyword,))
                
                row = cursor.fetchone()
                if row:
                    columns = [description[0] for description in cursor.description]
                    data = dict(zip(columns, row))
                    
                    # Parse JSON data
                    if data['trend_data']:
                        data['trend_data'] = json.loads(data['trend_data'])
                    if data['country_data']:
                        data['country_data'] = json.loads(data['country_data'])
                    
                    return data
                return None
                
        except Exception as e:
            print(f"Error getting keyword data: {e}")
            return None
    
    def get_cached_result(self, keyword: str) -> Optional[Dict[str, Any]]:
        """Get cached analysis result if still valid"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM keyword_analysis 
                    WHERE keyword = ? AND updated_at > ?
                ''', (keyword, datetime.now() - Config.CACHE_DURATION))
                
                row = cursor.fetchone()
                if row:
                    columns = [description[0] for description in cursor.description]
                    data = dict(zip(columns, row))
                    
                    # Parse JSON data
                    if data['trend_data']:
                        data['trend_data'] = json.loads(data['trend_data'])
                    if data['country_data']:
                        data['country_data'] = json.loads(data['country_data'])
                    
                    # Add cache indicator
                    data['from_cache'] = True
                    return data
                return None
                
        except Exception as e:
            print(f"Error getting cached result: {e}")
            return None
    
    def get_analysis_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent analysis history"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT keyword, monthly_searches, competition_level, 
                           difficulty_score, created_at
                    FROM keyword_analysis 
                    ORDER BY updated_at DESC 
                    LIMIT ?
                ''', (limit,))
                
                rows = cursor.fetchall()
                columns = [description[0] for description in cursor.description]
                
                return [dict(zip(columns, row)) for row in rows]
                
        except Exception as e:
            print(f"Error getting analysis history: {e}")
            return []
    
    def log_search(self, keyword: str, user_ip: str = None, response_time: float = None):
        """Log search activity"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO search_history (keyword, user_ip, response_time)
                    VALUES (?, ?, ?)
                ''', (keyword, user_ip, response_time))
                conn.commit()
                
        except Exception as e:
            print(f"Error logging search: {e}")
    
    def cleanup_old_data(self, days: int = 30):
        """Clean up old data from database"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Clean old search history
                cursor.execute('''
                    DELETE FROM search_history 
                    WHERE search_timestamp < ?
                ''', (cutoff_date,))
                
                # Clean expired cache
                cursor.execute('''
                    DELETE FROM scraping_cache 
                    WHERE expires_at < ?
                ''', (datetime.now(),))
                
                conn.commit()
                
        except Exception as e:
            print(f"Error cleaning up old data: {e}")
